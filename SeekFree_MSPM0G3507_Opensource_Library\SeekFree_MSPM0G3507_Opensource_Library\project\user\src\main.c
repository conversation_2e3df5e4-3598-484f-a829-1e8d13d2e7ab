/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          mian
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
********************************************************************************************************************/

#include "zf_common_headfile.h"
// 打开新的工程或者工程移植了位置务必执行以下操作
// 第一步 关闭上面所有打开的文件
// 第二步 project->clean  等待下方进度条走完

// 本例程集成感为8路灰度传感器驱动
// 替换原有的数字GPIO灰度传感器读取方式
// 提供更精确的模拟量读取和校准功能

// **************************** 代码区域 ****************************

//电机驱动
#define PIT_CH                  (PIT_TIM_A0 )                                      // 使用的周期中断编号 如果修改 需要同步对应修改周期中断编号与 isr.c 中的调用
#define PIT_PRIORITY            (TIMA0_INT_IRQn)                                      // 对应周期中断的中断编号

//感为8路灰度传感器配置
#define GANWEI_ADDR0_PIN        (A15)                                              // 地址线0引脚
#define GANWEI_ADDR1_PIN        (A8)                                               // 地址线1引脚
#define GANWEI_ADDR2_PIN        (A13)                                              // 地址线2引脚
#define GANWEI_ADC_PIN          (ADC0_CH0_A27)                                     // ADC通道引脚

uint8 pit_state = 0;
void pit_handler (uint32 state, void *ptr);

#define MOTOR1_DIR               (A25 )
#define MOTOR1_PWM               (PWM_TIM_G8_CH0_A29)

#define MOTOR2_DIR               (A28 )
#define MOTOR2_PWM               (PWM_TIM_G6_CH1_B7 )

#define LED1                     (B9 )
int turn = 0;

// 感为8路灰度传感器相关变量
ganwei_grayscale_info_struct grayscale_sensor;                                    // 传感器信息结构体
uint16 grayscale_analog[8];                                                       // 模拟量数据数组
uint8 grayscale_digital = 0;                                                      // 数字化状态

// 感为传感器校准数据（示例值，实际使用时需要根据环境校准）
uint16 white_calibration[8] = {3800, 3800, 3800, 3800, 3800, 3800, 3800, 3800}; // 白色校准值
uint16 black_calibration[8] = {500, 500, 500, 500, 500, 500, 500, 500};          // 黑色校准值

void pit_handler (uint32 state, void *ptr);

//感为8路灰度传感器循迹算法
int ganwei_loop(void)
{
    // 获取感为传感器的数字化状态
    grayscale_digital = ganwei_grayscale_get_digital(&grayscale_sensor);

    // 提取各个传感器的状态（位操作）
    int S1 = (grayscale_digital >> 0) & 0x01;  // 传感器1状态
    int S2 = (grayscale_digital >> 1) & 0x01;  // 传感器2状态
    int S3 = (grayscale_digital >> 2) & 0x01;  // 传感器3状态
    int S4 = (grayscale_digital >> 3) & 0x01;  // 传感器4状态
    int S5 = (grayscale_digital >> 4) & 0x01;  // 传感器5状态
    int S6 = (grayscale_digital >> 5) & 0x01;  // 传感器6状态
    int S7 = (grayscale_digital >> 6) & 0x01;  // 传感器7状态
    int S8 = (grayscale_digital >> 7) & 0x01;  // 传感器8状态

    // 使用与原代码相同的权重系数计算转向值
    int SP1 = S1 * (-35);
    int SP2 = S2 * (-30);
    int SP3 = S3 * (-6);
    int SP4 = S4 * (-4);
    int SP5 = S5 * (4);
    int SP6 = S6 * (6);
    int SP7 = S7 * (30);
    int SP8 = S8 * (35);

    int Turn = SP1 + SP2 + SP3 + SP4 + SP5 + SP6 + SP7 + SP8;

    gpio_toggle_level(LED1);  // LED状态指示
    return Turn;
}

int main (void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();					// 调试串口信息初始化

	// 此处编写用户代码 例如外设初始化代码等
	 gpio_init(MOTOR1_DIR, GPO, GPIO_HIGH, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
    pwm_init(MOTOR1_PWM, 17000, 0);                                                  // PWM 通道初始化频率 17KHz 占空比初始为 0
    gpio_init(LED1, GPO, GPIO_LOW, GPO_PUSH_PULL);                              // 初始化 LED1 输出 默认低电平 推挽输出模式
    gpio_init(MOTOR2_DIR, GPO, GPIO_HIGH, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
    pwm_init(MOTOR2_PWM, 17000, 0);                                                  // PWM 通道初始化频率 17KHz 占空比初始为 0

	  gpio_init(LED1, GPO, GPIO_LOW, GPO_PUSH_PULL);                              // 初始化 LED1 输出 默认低电平 推挽输出模式

	// 初始化感为8路灰度传感器
	ganwei_grayscale_init(&grayscale_sensor,
	                      GANWEI_GRAYSCALE_CLASS_EDITION,                           // 经典版传感器
	                      GANWEI_GRAYSCALE_ADC_12BITS,                             // 12位ADC分辨率
	                      GANWEI_ADDR0_PIN,                                        // 地址线0
	                      GANWEI_ADDR1_PIN,                                        // 地址线1
	                      GANWEI_ADDR2_PIN,                                        // 地址线2
	                      GANWEI_ADC_PIN);                                         // ADC通道

	// 使用校准数据初始化传感器
	ganwei_grayscale_init_with_calibration(&grayscale_sensor, white_calibration, black_calibration);

	float speed = 15;

	int leftSpeed ;
  int rightSpeed ;
	 pit_ms_init(PIT_TIM_A0, 20,pit_handler,NULL);                               // 初始化 PIT_CH0 为周期中断 20ms 周期
    interrupt_set_priority(PIT_PRIORITY, 0);                                    // 设置 PIT1 对周期中断的中断优先级为 0
    // 此处编写用户代码 例如外设初始化代码等
    while(true)
    {
		leftSpeed = (speed + turn );
		rightSpeed = (speed - turn )+2;

		if(leftSpeed  < 0)
		{
			leftSpeed = 0;
		}

		if(rightSpeed  < 0)
		{
			rightSpeed = 0;
		}


		gpio_set_level(MOTOR1_DIR, GPIO_HIGH);                                         // DIR输出高电平
        pwm_set_duty(MOTOR1_PWM, leftSpeed * (PWM_DUTY_MAX / 100));                   // 计算占空比

		 gpio_set_level(MOTOR2_DIR, GPIO_HIGH);                                         // DIR输出高电平
        pwm_set_duty(MOTOR2_PWM, rightSpeed * (PWM_DUTY_MAX / 100));                   // 计算占空比

		// 可选：打印调试信息（每1000次循环打印一次，避免输出过多）
		static uint32 debug_counter = 0;
		debug_counter++;
		if(debug_counter >= 1000)
		{
			debug_counter = 0;
			printf("Grayscale Digital: 0x%02X, Turn: %d, Left: %d, Right: %d\r\n",
			       grayscale_digital, turn, leftSpeed, rightSpeed);
		}

        // 此处编写需要循环执行的代码
    }
}

void pit_handler (uint32 state, void *ptr)
{
    // 在中断中调用感为传感器任务处理
    ganwei_grayscale_task(&grayscale_sensor);                                   // 传感器数据采集和处理
    turn = ganwei_loop();                                                        // 计算转向值
}

