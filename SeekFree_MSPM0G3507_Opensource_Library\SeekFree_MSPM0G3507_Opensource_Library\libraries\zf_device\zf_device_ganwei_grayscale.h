/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          zf_device_ganwei_grayscale
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
*
* 修改记录
* 日期              作者                备注
* 2025-01-01       SeekFree            感为8路灰度传感器驱动
********************************************************************************************************************/

#ifndef _ZF_DEVICE_GANWEI_GRAYSCALE_H_
#define _ZF_DEVICE_GANWEI_GRAYSCALE_H_

#include "zf_common_typedef.h"
#include "zf_driver_gpio.h"
#include "zf_driver_adc.h"

//====================================================感为8路灰度传感器 基础定义====================================================
#define GANWEI_GRAYSCALE_CHANNEL_NUM        8                                   // 传感器通道数量

//====================================================感为8路灰度传感器 传感器版本====================================================
typedef enum
{
    GANWEI_GRAYSCALE_CLASS_EDITION = 0,                                         // 经典版传感器
    GANWEI_GRAYSCALE_YOUTH_EDITION = 1,                                         // 青春版传感器
}ganwei_grayscale_edition_enum;

//====================================================感为8路灰度传感器 ADC分辨率====================================================
typedef enum
{
    GANWEI_GRAYSCALE_ADC_8BITS  = 0,                                           // 8位ADC模式
    GANWEI_GRAYSCALE_ADC_10BITS = 1,                                           // 10位ADC模式
    GANWEI_GRAYSCALE_ADC_12BITS = 2,                                           // 12位ADC模式
    GANWEI_GRAYSCALE_ADC_14BITS = 3,                                           // 14位ADC模式
}ganwei_grayscale_adc_bits_enum;

//====================================================感为8路灰度传感器 数据结构====================================================
typedef struct
{
    uint16 analog_value[GANWEI_GRAYSCALE_CHANNEL_NUM];                         // 原始模拟量值
    uint16 normal_value[GANWEI_GRAYSCALE_CHANNEL_NUM];                         // 归一化后的值
    uint16 calibrated_white[GANWEI_GRAYSCALE_CHANNEL_NUM];                     // 白校准基准值
    uint16 calibrated_black[GANWEI_GRAYSCALE_CHANNEL_NUM];                     // 黑校准基准值
    uint16 gray_white[GANWEI_GRAYSCALE_CHANNEL_NUM];                           // 白平衡灰度值
    uint16 gray_black[GANWEI_GRAYSCALE_CHANNEL_NUM];                           // 黑平衡灰度值
    float normal_factor[GANWEI_GRAYSCALE_CHANNEL_NUM];                         // 归一化系数
    float adc_max_value;                                                       // ADC最大值
    uint8 digital_value;                                                       // 数字输出状态(8位，每位对应一个传感器)
    uint8 timeout_value;                                                       // 超时时间设置
    uint8 tick_counter;                                                        // 时基计数器
    uint8 init_flag;                                                           // 初始化完成标志
    
    // 硬件配置
    ganwei_grayscale_edition_enum edition;                                     // 传感器版本
    ganwei_grayscale_adc_bits_enum adc_resolution;                             // ADC分辨率
    gpio_pin_enum addr_pin0;                                                   // 地址线0引脚
    gpio_pin_enum addr_pin1;                                                   // 地址线1引脚
    gpio_pin_enum addr_pin2;                                                   // 地址线2引脚
    adc_pin_enum adc_channel;                                                  // ADC通道
    uint8 direction_reverse;                                                   // 输出方向反转标志
}ganwei_grayscale_info_struct;

//====================================================感为8路灰度传感器 函数声明====================================================
uint8   ganwei_grayscale_init                   (ganwei_grayscale_info_struct *dev_info, ganwei_grayscale_edition_enum edition, ganwei_grayscale_adc_bits_enum adc_bits, gpio_pin_enum addr0, gpio_pin_enum addr1, gpio_pin_enum addr2, adc_pin_enum adc_ch);
uint8   ganwei_grayscale_init_with_calibration  (ganwei_grayscale_info_struct *dev_info, uint16 *white_values, uint16 *black_values);
void    ganwei_grayscale_task                   (ganwei_grayscale_info_struct *dev_info);
uint8   ganwei_grayscale_get_digital            (ganwei_grayscale_info_struct *dev_info);
uint8   ganwei_grayscale_get_analog             (ganwei_grayscale_info_struct *dev_info, uint16 *result);
uint8   ganwei_grayscale_get_normalized         (ganwei_grayscale_info_struct *dev_info, uint16 *result);
void    ganwei_grayscale_set_direction          (ganwei_grayscale_info_struct *dev_info, uint8 reverse);

#endif