# 感为8路灰度传感器移植说明

## 概述
本文档说明了如何将感为8路灰度传感器驱动集成到MSPM0G3507项目中，替换原有的数字GPIO灰度传感器读取方式。

## 移植内容

### 1. 硬件连接配置
```c
// 感为8路灰度传感器引脚配置
#define GANWEI_ADDR0_PIN        (A15)                  // 地址线0引脚
#define GANWEI_ADDR1_PIN        (A8)                   // 地址线1引脚  
#define GANWEI_ADDR2_PIN        (A13)                  // 地址线2引脚
#define GANWEI_ADC_PIN          (ADC0_CH0_A27)         // ADC通道引脚
```

### 2. 硬件连接说明
- **地址线连接**：A15, A8, A13 分别连接到感为传感器的ADDR0, ADDR1, ADDR2
- **ADC连接**：A27引脚连接到感为传感器的模拟输出
- **电源连接**：VCC接3.3V，GND接地

### 3. 软件功能特点
- **模拟量读取**：通过ADC获取精确的模拟量数据
- **数字化处理**：自动将模拟量转换为数字信号
- **校准功能**：支持白色和黑色校准，提高检测精度
- **兼容性**：保持与原代码相同的循迹算法和权重系数

### 4. 校准数据配置
```c
// 校准数据（需要根据实际环境调整）
uint16 white_calibration[8] = {3800, 3800, 3800, 3800, 3800, 3800, 3800, 3800};
uint16 black_calibration[8] = {500, 500, 500, 500, 500, 500, 500, 500};
```

### 5. 使用方法

#### 初始化
```c
// 初始化感为8路灰度传感器
ganwei_grayscale_init(&grayscale_sensor, 
                      GANWEI_GRAYSCALE_CLASS_EDITION,     // 经典版传感器
                      GANWEI_GRAYSCALE_ADC_12BITS,        // 12位ADC分辨率
                      GANWEI_ADDR0_PIN,                   // 地址线0
                      GANWEI_ADDR1_PIN,                   // 地址线1
                      GANWEI_ADDR2_PIN,                   // 地址线2
                      GANWEI_ADC_PIN);                    // ADC通道

// 使用校准数据初始化
ganwei_grayscale_init_with_calibration(&grayscale_sensor, white_calibration, black_calibration);
```

#### 数据读取
```c
// 在中断中调用传感器任务处理
ganwei_grayscale_task(&grayscale_sensor);               // 传感器数据采集和处理
turn = ganwei_loop();                                    // 计算转向值
```

### 6. 调试功能
代码中包含调试输出功能，可以通过串口查看传感器状态：
```
Grayscale Digital: 0x1F, Turn: 15, Left: 30, Right: 2
```

### 7. 校准步骤
1. **白色校准**：将传感器放在白色表面上，记录各通道的ADC值
2. **黑色校准**：将传感器放在黑色线条上，记录各通道的ADC值
3. **更新校准数据**：将记录的值填入`white_calibration`和`black_calibration`数组

### 8. 注意事项
- 确保硬件连接正确，特别是地址线的连接顺序
- 校准数据需要根据实际使用环境进行调整
- ADC引脚A27需要确保没有被其他功能占用
- 建议在稳定的光照条件下进行校准

### 9. 故障排除
- **传感器无响应**：检查电源和地址线连接
- **读数异常**：重新进行校准，调整校准数据
- **循迹效果差**：调整权重系数或重新校准

## 技术支持
如有问题，请参考感为传感器官方文档或联系技术支持。
