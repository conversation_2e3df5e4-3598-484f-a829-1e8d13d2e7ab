/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          mian
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2025-06-1        SeekFree            first version
********************************************************************************************************************/

#include "zf_common_headfile.h"
// 打开新的工程或者工程移动了位置务必执行以下操作
// 第一步 关闭上面所有打开的文件
// 第二步 project->clean  等待下方进度条走完

// *************************** 例程测试说明 ***************************

//电机驱动
#define PIT_CH                  (PIT_TIM_A0 )                                      // 使用的周期中断编号 如果修改 需要同步对应修改周期中断编号与 isr.c 中的调用
#define PIT_PRIORITY            (TIMA0_INT_IRQn)                                      // 对应周期中断的中断编号 


uint8 pit_state = 0;
void pit_handler (uint32 state, void *ptr);

#define MOTOR1_DIR               (A25 )
#define MOTOR1_PWM               (PWM_TIM_G8_CH0_A29)

#define MOTOR2_DIR               (A28 )
#define MOTOR2_PWM               (PWM_TIM_G6_CH1_B7 )


#define LED1                     (B9 )
int turn = 0;

void pit_handler (uint32 state, void *ptr);

//灰度
int loop( )
{
//	pd_init(&pd, );
	int S1= gpio_get_level(A12);
	int S2= gpio_get_level(A9);
	int S3= gpio_get_level(A7);
	int S4= gpio_get_level(A15);
	int S5= gpio_get_level(A8);
	int S6= gpio_get_level(A13);
	int S7= gpio_get_level(B12);
	int S8= gpio_get_level(B13);
	
	
	int SP1 = S1 * (-35);
	int SP2 = S2 * (-30);
	int SP3 = S3 * (-6);
	int SP4 = S4 * (-4);
	
  int SP5 = S5 * (4);
	int SP6 = S6 * (6);
	int SP7 = S7 * (30);
	int SP8 = S8 * (35);
	
	int Turn = SP1+SP2+SP3+SP4+SP5+SP6+SP7+SP8;
	
	
	
	 gpio_toggle_level(LED1);
	return Turn;
}

int main (void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();					// 调试串口信息初始化
	 
	// 此处编写用户代码 例如外设初始化代码等
	 gpio_init(MOTOR1_DIR, GPO, GPIO_HIGH, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
    pwm_init(MOTOR1_PWM, 17000, 0);                                                  // PWM 通道初始化频率 17KHz 占空比初始为 0
    gpio_init(LED1, GPO, GPIO_LOW, GPO_PUSH_PULL);                              // 初始化 LED1 输出 默认低电平 推挽输出模式
    gpio_init(MOTOR2_DIR, GPO, GPIO_HIGH, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
    pwm_init(MOTOR2_PWM, 17000, 0);                                                  // PWM 通道初始化频率 17KHz 占空比初始为 0
	
	  gpio_init(LED1, GPO, GPIO_LOW, GPO_PUSH_PULL);                              // 初始化 LED1 输出 默认低电平 推挽输出模式
	
	
	gpio_init(A12, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(A9, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(A7, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(A15, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(A8, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(A13, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(B12, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	gpio_init(B13, GPI, GPIO_HIGH, GPI_FLOATING_IN);
	
	float speed = 15;
	
	
	int leftSpeed ;
  int rightSpeed ;
	 pit_ms_init(PIT_TIM_A0, 20,pit_handler,NULL);                               // 初始化 PIT_CH0 为周期中断 1000ms 周期
    interrupt_set_priority(PIT_PRIORITY, 0);                                    // 设置 PIT1 对周期中断的中断优先级为 0
    // 此处编写用户代码 例如外设初始化代码等
    while(true)
    {
			leftSpeed = (speed + turn );
			rightSpeed = (speed - turn )+2;
			
			if(leftSpeed  < 0)
			{
				leftSpeed = 0;
			}
			
			if(rightSpeed  < 0)
			{
				rightSpeed = 0;
			}
			
			

			gpio_set_level(MOTOR1_DIR, GPIO_HIGH);                                         // DIR输出高电平
            pwm_set_duty(MOTOR1_PWM, leftSpeed * (PWM_DUTY_MAX / 100));                   // 计算占空比
			
			 gpio_set_level(MOTOR2_DIR, GPIO_HIGH);                                         // DIR输出高电平
            pwm_set_duty(MOTOR2_PWM, rightSpeed * (PWM_DUTY_MAX / 100));                   // 计算占空比
			
			
        // 此处编写需要循环执行的代码
    
  }
}
void pit_handler (uint32 state, void *ptr)
{
	        
	         turn = loop( );                                                      // 周期中断触发 标志位置位
}
